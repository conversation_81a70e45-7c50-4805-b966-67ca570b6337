{"buildCommand": "npm run build", "outputDirectory": "dist/people-manager", "installCommand": "npm install", "framework": "angular", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "build": {"env": {"NODE_OPTIONS": "--openssl-legacy-provider"}}}