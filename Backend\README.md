# Person Management RESTful API

A complete RESTful Web Service built with Node.js, Express, and MongoDB for managing people data.

## Features

- **Full CRUD Operations**: Create, Read, Update, Delete people
- **RESTful API**: JSON API endpoints for integration
- **Web Interface**: HTML forms and pages for browser interaction
- **MongoDB Integration**: Persistent data storage
- **Responsive Design**: Bootstrap-based UI
- **Form Validation**: Client and server-side validation
- **Error Handling**: Comprehensive error management

## API Endpoints

### Web Interface (HTML)
- `GET /person` - Display table with list of people
- `GET /person/new` - Display form to create a new person
- `POST /person` - Handle form submission to create person
- `GET /person/:id/edit` - Display form to edit a person
- `PUT /person/:id` - Handle form submission to update person
- `GET /person/:id/delete` - Display confirmation page for deletion
- `DELETE /person/:id` - Handle deletion of person

### JSON API (for Frontend integration)
- `GET /person` (with Accept: application/json) - Get all people as JSON
- `POST /person` (with Accept: application/json) - Create person via JSON
- `GET /person/:id` (with Accept: application/json) - Get specific person as JSON
- `PUT /person/:id` (with Accept: application/json) - Update person via JSON
- `DELETE /person/:id` (with Accept: application/json) - Delete person via JSON

## Person Schema

```javascript
{
  firstName: String (required, min 2 chars),
  lastName: String (required, min 2 chars),
  email: String (required, unique, valid email),
  phone: String (optional, valid phone format),
  address: String (optional),
  dateOfBirth: Date (optional),
  createdAt: Date (auto-generated),
  updatedAt: Date (auto-generated)
}
```

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn

## Installation

1. **Clone or navigate to the Backend directory**
   ```bash
   cd Backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the Backend directory:
   ```env
   PORT=3000
   MONGODB_URI=mongodb://localhost:27017/person_management
   NODE_ENV=development
   ```

4. **Start MongoDB**
   - For local MongoDB: `mongod`
   - For MongoDB Atlas: Use your connection string in MONGODB_URI

5. **Run the application**
   ```bash
   # Development mode (with nodemon)
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Access the application**
   - Web Interface: http://localhost:3000
   - API Health Check: http://localhost:3000/api/health

## Usage Examples

### Web Interface
1. Visit http://localhost:3000 to see the people list
2. Click "Add New Person" to create a person
3. Click edit icon to modify a person
4. Click delete icon to remove a person

### API Usage (JSON)

**Get all people:**
```bash
curl -H "Accept: application/json" http://localhost:3000/person
```

**Create a person:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "************",
    "address": "123 Main St, City, State"
  }' \
  http://localhost:3000/person
```

**Update a person:**
```bash
curl -X PUT \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "firstName": "Jane",
    "lastName": "Doe",
    "email": "<EMAIL>"
  }' \
  http://localhost:3000/person/PERSON_ID
```

**Delete a person:**
```bash
curl -X DELETE \
  -H "Accept: application/json" \
  http://localhost:3000/person/PERSON_ID
```

## Project Structure

```
Backend/
├── config/
│   └── database.js          # MongoDB connection
├── models/
│   └── Person.js           # Person schema/model
├── routes/
│   └── personRoutes.js     # API routes
├── views/
│   ├── layout.ejs          # Base template
│   ├── people-list.ejs     # People list page
│   ├── person-form.ejs     # Add/Edit form
│   ├── person-delete.ejs   # Delete confirmation
│   └── error.ejs           # Error page
├── public/
│   ├── css/
│   │   └── style.css       # Custom styles
│   └── js/
│       └── main.js         # Client-side JavaScript
├── .env                    # Environment variables
├── package.json            # Dependencies
├── server.js              # Main server file
└── README.md              # This file
```

## Technologies Used

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose ODM
- **Template Engine**: EJS
- **Frontend**: Bootstrap 5, Font Awesome
- **Validation**: Mongoose validators, client-side validation
- **Environment**: dotenv for configuration

## Error Handling

The application includes comprehensive error handling:
- MongoDB connection errors
- Validation errors (required fields, email format, etc.)
- 404 errors for non-existent resources
- 500 errors for server issues
- Graceful error pages for web interface
- JSON error responses for API calls

## Development

For development with auto-restart:
```bash
npm run dev
```

This uses nodemon to automatically restart the server when files change.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

ISC License
