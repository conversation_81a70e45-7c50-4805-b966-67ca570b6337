<% 
const body = `
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        People List
    </h1>
    <a href="/person/new" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Add New Person
    </a>
</div>

<% if (people && people.length > 0) { %>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Address</th>
                            <th>Date of Birth</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% people.forEach(person => { %>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <%= person.firstName.charAt(0).toUpperCase() + person.lastName.charAt(0).toUpperCase() %>
                                        </div>
                                        <div>
                                            <strong><%= person.firstName %> <%= person.lastName %></strong>
                                        </div>
                                    </div>
                                </td>
                                <td><%= person.email %></td>
                                <td><%= person.phone || '-' %></td>
                                <td><%= person.address || '-' %></td>
                                <td>
                                    <%= person.dateOfBirth ? new Date(person.dateOfBirth).toLocaleDateString() : '-' %>
                                </td>
                                <td>
                                    <%= new Date(person.createdAt).toLocaleDateString() %>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/person/<%= person._id %>/edit" class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/person/<%= person._id %>/delete" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer text-muted">
            Total: <%= people.length %> person<%= people.length !== 1 ? 's' : '' %>
        </div>
    </div>
<% } else { %>
    <div class="text-center py-5">
        <i class="fas fa-users fa-3x text-muted mb-3"></i>
        <h3 class="text-muted">No People Found</h3>
        <p class="text-muted">Get started by adding your first person.</p>
        <a href="/person/new" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add First Person
        </a>
    </div>
<% } %>
`;
%>

<%- include('layout', { title, body, message: typeof message !== 'undefined' ? message : null }) %>
