const express = require('express');
const router = express.Router();
const Person = require('../models/Person');

// GET /person - Display a table with a list of people
router.get('/', async (req, res) => {
    try {
        const people = await Person.find().sort({ createdAt: -1 });
        
        // Check if request wants JSON (API call) or HTML (browser)
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.json({
                success: true,
                data: people,
                count: people.length
            });
        }
        
        // Render HTML page with table
        res.render('people-list', { 
            title: 'People List',
            people: people,
            message: req.query.message || null
        });
    } catch (error) {
        console.error('Error fetching people:', error);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.status(500).json({
                success: false,
                message: 'Error fetching people',
                error: error.message
            });
        }
        
        res.render('error', { 
            title: 'Error',
            message: 'Error fetching people',
            error: error.message
        });
    }
});

// GET /person/new - Display form to create a new person
router.get('/new', (req, res) => {
    res.render('person-form', {
        title: 'Add New Person',
        person: {},
        action: '/person',
        method: 'POST',
        isEdit: false
    });
});

// POST /person - Handle form submission to create a new person
router.post('/', async (req, res) => {
    try {
        const { firstName, lastName, email, phone, address, dateOfBirth } = req.body;
        
        const newPerson = new Person({
            firstName,
            lastName,
            email,
            phone,
            address,
            dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined
        });
        
        const savedPerson = await newPerson.save();
        
        // Check if request wants JSON (API call) or HTML (browser)
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.status(201).json({
                success: true,
                data: savedPerson,
                message: 'Person created successfully'
            });
        }
        
        // Redirect to list with success message
        res.redirect('/person?message=Person created successfully');
    } catch (error) {
        console.error('Error creating person:', error);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.status(400).json({
                success: false,
                message: 'Error creating person',
                error: error.message
            });
        }
        
        // Re-render form with error
        res.render('person-form', {
            title: 'Add New Person',
            person: req.body,
            action: '/person',
            method: 'POST',
            isEdit: false,
            error: error.message
        });
    }
});

// GET /person/:id - Get a specific person (API endpoint)
router.get('/:id', async (req, res) => {
    try {
        const person = await Person.findById(req.params.id);
        
        if (!person) {
            if (req.headers.accept && req.headers.accept.includes('application/json')) {
                return res.status(404).json({
                    success: false,
                    message: 'Person not found'
                });
            }
            return res.render('error', {
                title: 'Error',
                message: 'Person not found'
            });
        }
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.json({
                success: true,
                data: person
            });
        }
        
        // For browser requests, redirect to edit form
        res.redirect(`/person/${req.params.id}/edit`);
    } catch (error) {
        console.error('Error fetching person:', error);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.status(500).json({
                success: false,
                message: 'Error fetching person',
                error: error.message
            });
        }
        
        res.render('error', {
            title: 'Error',
            message: 'Error fetching person',
            error: error.message
        });
    }
});

// GET /person/:id/edit - Display form to edit a person
router.get('/:id/edit', async (req, res) => {
    try {
        const person = await Person.findById(req.params.id);
        
        if (!person) {
            return res.render('error', {
                title: 'Error',
                message: 'Person not found'
            });
        }
        
        res.render('person-form', {
            title: 'Edit Person',
            person: person,
            action: `/person/${person._id}?_method=PUT`,
            method: 'POST',
            isEdit: true
        });
    } catch (error) {
        console.error('Error fetching person for edit:', error);
        res.render('error', {
            title: 'Error',
            message: 'Error fetching person',
            error: error.message
        });
    }
});

// PUT /person/:id - Handle form submission to update a person
router.put('/:id', async (req, res) => {
    try {
        const { firstName, lastName, email, phone, address, dateOfBirth } = req.body;
        
        const updateData = {
            firstName,
            lastName,
            email,
            phone,
            address,
            dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined
        };
        
        const updatedPerson = await Person.findByIdAndUpdate(
            req.params.id,
            updateData,
            { new: true, runValidators: true }
        );
        
        if (!updatedPerson) {
            if (req.headers.accept && req.headers.accept.includes('application/json')) {
                return res.status(404).json({
                    success: false,
                    message: 'Person not found'
                });
            }
            return res.render('error', {
                title: 'Error',
                message: 'Person not found'
            });
        }
        
        // Check if request wants JSON (API call) or HTML (browser)
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.json({
                success: true,
                data: updatedPerson,
                message: 'Person updated successfully'
            });
        }
        
        // Redirect to list with success message
        res.redirect('/person?message=Person updated successfully');
    } catch (error) {
        console.error('Error updating person:', error);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.status(400).json({
                success: false,
                message: 'Error updating person',
                error: error.message
            });
        }
        
        // Re-render form with error
        const person = await Person.findById(req.params.id);
        res.render('person-form', {
            title: 'Edit Person',
            person: { ...person.toObject(), ...req.body },
            action: `/person/${req.params.id}?_method=PUT`,
            method: 'POST',
            isEdit: true,
            error: error.message
        });
    }
});

// GET /person/:id/delete - Display confirmation page for deletion
router.get('/:id/delete', async (req, res) => {
    try {
        const person = await Person.findById(req.params.id);
        
        if (!person) {
            return res.render('error', {
                title: 'Error',
                message: 'Person not found'
            });
        }
        
        res.render('person-delete', {
            title: 'Delete Person',
            person: person
        });
    } catch (error) {
        console.error('Error fetching person for deletion:', error);
        res.render('error', {
            title: 'Error',
            message: 'Error fetching person',
            error: error.message
        });
    }
});

// DELETE /person/:id - Handle deletion of a person
router.delete('/:id', async (req, res) => {
    try {
        const deletedPerson = await Person.findByIdAndDelete(req.params.id);
        
        if (!deletedPerson) {
            if (req.headers.accept && req.headers.accept.includes('application/json')) {
                return res.status(404).json({
                    success: false,
                    message: 'Person not found'
                });
            }
            return res.render('error', {
                title: 'Error',
                message: 'Person not found'
            });
        }
        
        // Check if request wants JSON (API call) or HTML (browser)
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.json({
                success: true,
                message: 'Person deleted successfully',
                data: deletedPerson
            });
        }
        
        // Redirect to list with success message
        res.redirect('/person?message=Person deleted successfully');
    } catch (error) {
        console.error('Error deleting person:', error);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            return res.status(500).json({
                success: false,
                message: 'Error deleting person',
                error: error.message
            });
        }
        
        res.render('error', {
            title: 'Error',
            message: 'Error deleting person',
            error: error.message
        });
    }
});

module.exports = router;
