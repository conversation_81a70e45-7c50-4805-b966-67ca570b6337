.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-icon {
  margin-right: 16px;
}

.app-title {
  font-size: 20px;
  font-weight: 500;
}

.spacer {
  flex: 1 1 auto;
}

.app-subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.main-content {
  flex: 1;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}