.person-edit-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-field {
  flex: 1;
}

.form-field.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.form-actions button {
  min-width: 120px;
}

.form-actions button mat-spinner {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .person-edit-container {
    padding: 10px;
  }

  .form-row {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .form-actions button {
    width: 100%;
  }
}