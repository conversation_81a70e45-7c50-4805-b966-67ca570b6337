{"name": "person-management-api", "version": "1.0.0", "description": "RESTful Web Service for Person Management using Node.js and MongoDB", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "mongodb", "rest-api", "express", "person-management"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "ejs": "^3.1.9", "method-override": "^3.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}