<% 
const body = `
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h2 class="card-title mb-0">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error
                </h2>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h4 class="text-danger">${message || 'An error occurred'}</h4>
                
                <% if (typeof error !== 'undefined' && error && process.env.NODE_ENV === 'development') { %>
                    <div class="alert alert-secondary mt-3">
                        <small><strong>Error Details:</strong> <%= error %></small>
                    </div>
                <% } %>
                
                <div class="mt-4">
                    <a href="/person" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>
                        Go to Home
                    </a>
                    <button onclick="history.back()" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        Go Back
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
`;
%>

<%- include('layout', { title, body }) %>
